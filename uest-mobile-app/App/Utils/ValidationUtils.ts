/**
 * Validation utility for student profile forms
 * Implements the same validation rules as the web application
 */

export interface ValidationError {
  [key: string]: string;
}

export interface StudentProfileData {
  firstName: string;
  middleName?: string;
  lastName: string;
  mothersName?: string;
  email: string;
  contact: string;
  contact2?: string;
  medium: string;
  classroom: string;
  gender?: string;
  birthday: Date | string | null;
  school: string;
  address: string;
  age?: string;
  photo: string;
  document: string;
  // Other Details
  aadhaarNumber?: string;
  bloodGroup?: string;
  birthPlace?: string;
  motherTongue?: string;
  religion?: string;
  caste?: string;
  subCaste?: string;
}

/**
 * Validates if a string starts with a letter and contains only letters and spaces
 */
export const validateNameField = (value: string, fieldName: string): string => {
  if (!value || !value.trim()) {
    return `${fieldName} is required`;
  }
  
  if (value.trim().length < 2) {
    return `${fieldName} must be at least 2 characters`;
  }
  
  if (!/^[a-zA-Z]/.test(value.trim())) {
    return `${fieldName} must start with a letter`;
  }
  
  if (!/^[a-zA-Z\s]*$/.test(value.trim())) {
    return `${fieldName} can only contain letters and spaces`;
  }
  
  return '';
};

/**
 * Validates email format
 */
export const validateEmail = (email: string): string => {
  if (!email || !email.trim()) {
    return 'Email is required';
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email.trim())) {
    return 'Please enter a valid email address';
  }
  
  return '';
};

/**
 * Validates contact number (exactly 10 digits)
 */
export const validateContactNumber = (contact: string, fieldName: string = 'Contact number'): string => {
  if (!contact || !contact.trim()) {
    return `${fieldName} is required`;
  }
  
  const cleanContact = contact.replace(/\D/g, '');
  
  if (cleanContact.length !== 10) {
    return `${fieldName} must be exactly 10 digits`;
  }
  
  if (!/^\d+$/.test(cleanContact)) {
    return `${fieldName} must contain only digits`;
  }
  
  return '';
};

/**
 * Validates Aadhaar number (exactly 12 digits)
 */
export const validateAadhaarNumber = (aadhaar: string): string => {
  if (!aadhaar || !aadhaar.trim()) {
    return ''; // Optional field
  }
  
  const cleanAadhaar = aadhaar.replace(/\D/g, '');
  
  if (cleanAadhaar.length !== 12) {
    return 'Aadhaar number must be exactly 12 digits';
  }
  
  if (!/^\d+$/.test(cleanAadhaar)) {
    return 'Aadhaar number must contain only digits';
  }
  
  return '';
};

/**
 * Validates date of birth
 */
export const validateDateOfBirth = (birthday: Date | string | null): string => {
  if (!birthday) {
    return 'Date of birth is required';
  }
  
  try {
    const date = typeof birthday === 'string' ? new Date(birthday) : birthday;
    const today = new Date();
    
    if (isNaN(date.getTime())) {
      return 'Please select a valid date of birth';
    }
    
    if (date > today) {
      return 'Date of birth cannot be in the future';
    }
    
    const minDate = new Date('1900-01-01');
    if (date < minDate) {
      return 'Please select a valid date of birth';
    }
    
    return '';
  } catch (error) {
    return 'Please select a valid date of birth';
  }
};

/**
 * Validates required text fields
 */
export const validateRequiredField = (value: string, fieldName: string, minLength: number = 1): string => {
  if (!value || !value.trim()) {
    return `${fieldName} is required`;
  }
  
  if (value.trim().length < minLength) {
    return `${fieldName} must be at least ${minLength} character${minLength > 1 ? 's' : ''}`;
  }
  
  return '';
};

/**
 * Validates photo field (mandatory)
 */
export const validatePhoto = (photo: string): string => {
  if (!photo || !photo.trim()) {
    return 'Student image is required. Please capture or upload a photo';
  }
  
  return '';
};

/**
 * Validates document field (mandatory)
 */
export const validateDocument = (document: string): string => {
  if (!document || !document.trim()) {
    return 'Identity document is required. Please upload a document';
  }
  
  return '';
};

/**
 * Main validation function for student profile
 */
export const validateStudentProfile = (data: Partial<StudentProfileData>): ValidationError => {
  const errors: ValidationError = {};
  
  // Personal Details - Required fields
  const firstNameError = validateNameField(data.firstName || '', 'First name');
  if (firstNameError) errors.firstName = firstNameError;
  
  const lastNameError = validateNameField(data.lastName || '', 'Last name');
  if (lastNameError) errors.lastName = lastNameError;
  
  // Middle name is optional but if provided, must follow name rules
  if (data.middleName && data.middleName.trim()) {
    const middleNameError = validateNameField(data.middleName, 'Middle name');
    if (middleNameError) errors.middleName = middleNameError;
  }
  
  // Mother's name is optional but if provided, must follow name rules
  if (data.mothersName && data.mothersName.trim()) {
    const mothersNameError = validateNameField(data.mothersName, "Mother's name");
    if (mothersNameError) errors.mothersName = mothersNameError;
  }
  
  const emailError = validateEmail(data.email || '');
  if (emailError) errors.email = emailError;
  
  const contactError = validateContactNumber(data.contact || '');
  if (contactError) errors.contact = contactError;
  
  // Contact2 is optional but if provided, must be valid
  if (data.contact2 && data.contact2.trim()) {
    const contact2Error = validateContactNumber(data.contact2, 'Contact number 2');
    if (contact2Error) errors.contact2 = contact2Error;
  }
  
  const mediumError = validateRequiredField(data.medium || '', 'Medium of instruction');
  if (mediumError) errors.medium = mediumError;
  
  const classroomError = validateRequiredField(data.classroom || '', 'Standard');
  if (classroomError) errors.classroom = classroomError;
  
  const birthdayError = validateDateOfBirth(data.birthday);
  if (birthdayError) errors.birthday = birthdayError;
  
  const schoolError = validateRequiredField(data.school || '', 'School name', 2);
  if (schoolError) errors.school = schoolError;
  
  const addressError = validateRequiredField(data.address || '', 'Address', 5);
  if (addressError) errors.address = addressError;
  
  // Photo and Document - Mandatory fields
  const photoError = validatePhoto(data.photo || '');
  if (photoError) errors.photo = photoError;
  
  const documentError = validateDocument(data.document || '');
  if (documentError) errors.document = documentError;
  
  // Other Details - Optional fields with validation
  if (data.aadhaarNumber && data.aadhaarNumber.trim()) {
    const aadhaarError = validateAadhaarNumber(data.aadhaarNumber);
    if (aadhaarError) errors.aadhaarNumber = aadhaarError;
  }
  
  if (data.birthPlace && data.birthPlace.trim()) {
    const birthPlaceError = validateNameField(data.birthPlace, 'Birth place');
    if (birthPlaceError) errors.birthPlace = birthPlaceError;
  }
  
  if (data.motherTongue && data.motherTongue.trim()) {
    const motherTongueError = validateNameField(data.motherTongue, 'Mother tongue');
    if (motherTongueError) errors.motherTongue = motherTongueError;
  }
  
  if (data.religion && data.religion.trim()) {
    const religionError = validateNameField(data.religion, 'Religion');
    if (religionError) errors.religion = religionError;
  }
  
  if (data.caste && data.caste.trim()) {
    const casteError = validateNameField(data.caste, 'Caste');
    if (casteError) errors.caste = casteError;
  }
  
  if (data.subCaste && data.subCaste.trim()) {
    const subCasteError = validateNameField(data.subCaste, 'Sub caste');
    if (subCasteError) errors.subCaste = subCasteError;
  }
  
  return errors;
};

/**
 * Helper function to check if there are any validation errors
 */
export const hasValidationErrors = (errors: ValidationError): boolean => {
  return Object.keys(errors).length > 0;
};

/**
 * Helper function to get the first error message
 */
export const getFirstErrorMessage = (errors: ValidationError): string => {
  const firstKey = Object.keys(errors)[0];
  return firstKey ? errors[firstKey] : '';
};
