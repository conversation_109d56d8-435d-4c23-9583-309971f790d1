import { z } from 'zod';

// Date validation
const dateValidation = z.string().refine(
  (val) => !isNaN(new Date(val).getTime()),
  { message: 'Invalid date format' }
);

// Basic schemas
export const createStudentProfileSchema = z.object({
  medium: z.string().min(1, 'Medium is required'),
  classroom: z.string().min(1, 'Classroom is required'),
  birthday: dateValidation,
  school: z.string().min(2, 'School name must be at least 2 characters'),
  address: z.string().min(5, 'Address must be at least 5 characters'),
});

export const updateStudentProfileSchema = z.object({
  medium: z.string().min(1, 'Medium is required').optional(),
  classroom: z.string().min(1, 'Classroom is required').optional(),
  birthday: dateValidation.optional(),
  school: z.string().min(2, 'School name must be at least 2 characters').optional(),
  address: z.string().min(5, 'Address must be at least 5 characters').optional(),
});

export const updateStudentProfileStatusSchema = z.object({
  status: z.enum(['PENDING', 'APPROVED', 'REJECTED'], {
    errorMap: () => ({ message: 'Status must be one of: PENDING, APPROVED, REJECTED' }),
  }),
});

export const updateStudentAndProfileSchema = z.object({
  // Student data
  firstName: z.string().min(2, 'Name must be at least 2 characters').optional(),
  lastName: z.string().min(2, 'Name must be at least 2 characters').optional(),
  contact: z.string()
    .min(10, 'Contact number must be at least 10 digits')
    .regex(/^\d+$/, 'Contact number must contain only digits')
    .optional(),

  // Profile data
  middleName: z.string().optional(),
  mothersName: z.string().optional(),
  email: z.string().email('Please enter a valid email address').optional(),
 contact2: z.preprocess(
    val => val === '' ? undefined : val, // Treat empty string as undefined
    z.string()
      .min(10, 'Contact number must be at least 10 digits')
      .regex(/^\d+$/, 'Contact number must contain only digits')
      .optional()
  ),
  medium: z.string().min(1, 'Medium is required').optional(),
  classroom: z.string().min(1, 'Classroom is required').optional(),
  gender: z.string().optional(),
  birthday: dateValidation.optional(),
  school: z.string().min(2, 'School name must be at least 2 characters').optional(),
  address: z.string().min(5, 'Address must be at least 5 characters').optional(),
  age: z.string().optional(),
 aadhaarNumber: z.preprocess(
    (val) => val === '' ? undefined : val,
    z.string()
      .min(12, 'Aadhaar number must be 12 digits')
      .max(12, 'Aadhaar number must be 12 digits')
      .regex(/^\d+$/, 'Aadhaar number must contain only digits')
      .optional()
  ),
  bloodGroup: z.string().optional(),
  birthPlace: z.string().optional(),
  motherTongue: z.string().optional(),
  religion: z.string().optional(),
  caste: z.string().optional(),
  subCaste: z.string().optional(),

  // Base64 image data
  photo: z.string().optional(),
  photoMimeType: z.string().optional(),
  document: z.string().optional(),
  documentMimeType: z.string().optional(),
  documentName: z.string().optional(),
});
